// repos/transferRepo.js
const admin = require("firebase-admin");
const db = admin.firestore();
const { findLastGrnItemPrice } = require("@/repositories/stockLedgerRepo");
const CLOSING_COLLECTION = "closing";
const INVENTORY_COLLECTION = "inventoryItems";
const INVENTORY_LOCATIONS_COLLECTION = "inventoryLocations";
const {
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");

async function fetchClosingData({
  tenantId,
  locations,
  inventoryLocations,
  fromDate,
  toDate,
  stockCorrection,
}) {
  let query = db.collection(CLOSING_COLLECTION).where("tenantId", "==", tenantId);

  if (locations?.length) query = query.where("locationId", "in", locations);
  if (inventoryLocations?.length)
    query = query.where("workAreaId", "in", inventoryLocations);

  if (fromDate) {
    query = query.where(
      "closedBy.time",
      ">=",
      FD.toFirestore(fromDate, TIME_OPTION.START)
    );
  }
  if (toDate) {
    query = query.where(
      "closedBy.time",
      "<=",
      FD.toFirestore(toDate, TIME_OPTION.END)
    );
  }

  if (stockCorrection === "yes") {
    query = query.where("stockCorrection", "==", true);
  } else if (stockCorrection === "no") {
    query = query.where("stockCorrection", "==", false);
  }

// Add sorting: last created first
query = query.orderBy("closingNumber", "desc");

  const snapshot = await query.get();
  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}


async function getById(id) {
  try {
    const docRef = db.collection(CLOSING_COLLECTION).doc(id);
    const snapshot = await docRef.get();

    if (!snapshot.exists) return null;

    const closingData = snapshot.data();

    // Get items from subcollection
    const itemsSnapshot = await docRef.collection("items").get();
    const items = itemsSnapshot.docs.map(doc => doc.data());

    return { ...closingData, items };
  } catch (err) {
    throw Error(err.message);
  }
}

async function fetchClosingItems(tenantId, locationId) {
  if (!tenantId) throw new Error("tenantId is required");
  if (!locationId) throw new Error("locationId is required");
  // Get location data
  const workArea = await db
    .collection(INVENTORY_LOCATIONS_COLLECTION)
    .where("id", "==", locationId)
    .get();
  if (workArea.empty) {
    console.log("No matching locations found", locationId, "tenantId", tenantId);
    return [];
  }
  const locationData = workArea.docs[0].data();
  let query = db
    .collection(INVENTORY_COLLECTION)
    .where("tenantId", "==", tenantId);

  // Add tag filter if tagId exists
  if (locationData.tagId) {
    query = query.where("tags", "array-contains", locationData.tagId);
  }
  const result = await query.get();
  return result.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  }));
}

async function createClosing(data, transaction = null) {
  const ref = db.collection(CLOSING_COLLECTION).doc();

  // Separate items from the main document
  const { items, ...closingData } = data;

  // Create the main closing document
  const closingDoc = { ...closingData, id: ref.id };

  if (transaction) {
    transaction.set(ref, closingDoc);

    // Save items to subcollection
    if (items && items.length > 0) {
      const itemsCollectionRef = ref.collection("items");
      for (const item of items) {
        const itemDocId = `${item.itemId}_${item.pkg.id}`;
        const itemRef = itemsCollectionRef.doc(itemDocId);
        transaction.set(itemRef, item);
      }
    }
  } else {
    await ref.set(closingDoc);

    // Save items to subcollection
    if (items && items.length > 0) {
      const batch = db.batch();
      const itemsCollectionRef = ref.collection("items");

      for (const item of items) {
        const itemDocId = `${item.itemId}_${item.pkg.id}`;
        const itemRef = itemsCollectionRef.doc(itemDocId);
        batch.set(itemRef, item);
      }

      await batch.commit();
    }
  }

  return { id: ref.id, ...data };
}

async function getClosingByNumber(tenantId, closingNumber) {
  let query = db
    .collection(CLOSING_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("closingNumber", "==", closingNumber)
    .limit(1);

  const snapshot = await query.get();
  if (snapshot.empty) return null;
  return snapshot.docs[0].data();
}

function convertOpenToClosingQuantity(item) {
  const { openQuantities, countingUOM, recipeUOM, closingQuantity, pkg } = item;
  
  if (!pkg || !pkg.quantity || !pkg.toUnit) {
    return {
      equivalentPackages: 0,
      closingQtyInRecipeUOM: 0
    };
  }

  const { quantity: pkgQuantity, toUnit, emptyWeight, fullWeight } = pkg;

  // Check if weights are available and valid (ALWAYS in KG)
  const hasValidWeights = 
    emptyWeight !== null && 
    emptyWeight !== undefined && 
    fullWeight !== null && 
    fullWeight !== undefined &&
    fullWeight > emptyWeight;

  let totalOpenInToUnit = 0;

  if (openQuantities && openQuantities.length > 0) {
    let totalOpenInCountingUOM;

    if (hasValidWeights) {
      // Weights available: openQuantities are MEASURED WEIGHTS in KG
      const liquidWeightKg = fullWeight - emptyWeight;
      const densityKgPerUnit = liquidWeightKg / pkgQuantity;
      
      // CRITICAL: Subtract container/box weight from each measured weight
      const volumesInToUnit = openQuantities.map(measuredWeightKg => {
        // Remove container weight to get pure content weight
        const contentOnlyWeightKg = measuredWeightKg - emptyWeight;
        
        // Convert weight to units (ml, pieces, grams, etc.)
        const volumeInToUnit = contentOnlyWeightKg / densityKgPerUnit;
        return volumeInToUnit;
      });

      const totalInToUnit = volumesInToUnit.reduce((sum, vol) => sum + vol, 0);

      // Convert from toUnit to countingUOM
      totalOpenInCountingUOM = convertUnit(totalInToUnit, toUnit, countingUOM);
    } else {
      // No weights: openQuantities already in countingUOM (direct count/measure)
      totalOpenInCountingUOM = openQuantities.reduce((sum, qty) => sum + qty, 0);
    }

    // Convert to toUnit for calculation
    totalOpenInToUnit = convertUnit(totalOpenInCountingUOM, countingUOM, toUnit);
  }

  // Calculate equivalent packages from open quantities
  const equivalentPackages = totalOpenInToUnit / pkgQuantity;

  // Calculate total closing quantity in toUnit
  const closingInToUnit = closingQuantity * pkgQuantity;

  // Total inventory in toUnit
  const totalInToUnit = closingInToUnit + totalOpenInToUnit;

  // Convert total to recipeUOM
  const closingQtyInRecipeUOM = recipeUOM 
    ? convertUnit(totalInToUnit, toUnit, recipeUOM)
    : totalInToUnit;

  return {
    equivalentPackages,
    closingQtyInRecipeUOM
  };
}

function convertUnit(value, fromUnit, toUnit) {
  const from = fromUnit.toLowerCase();
  const to = toUnit.toLowerCase();

  if (from === to) return value;

  // Volume units (only ml, l)
  const volumeUnits = ['ml', 'l'];
  const isVolumeConversion = volumeUnits.includes(from) && volumeUnits.includes(to);

  // Weight units (only g, kg)
  const weightUnits = ['g', 'kg'];
  const isWeightConversion = weightUnits.includes(from) && weightUnits.includes(to);

  // Count units (only nos)
  const countUnits = ['nos'];
  const isCountConversion = countUnits.includes(from) && countUnits.includes(to);

  if (isVolumeConversion) {
    const valueInMl = from === 'l' ? value * 1000 : value;
    return to === 'l' ? valueInMl / 1000 : valueInMl;
  } 
  
  if (isWeightConversion) {
    const valueInG = from === 'kg' ? value * 1000 : value;
    return to === 'kg' ? valueInG / 1000 : valueInG;
  }

  if (isCountConversion) {
    // "nos" to "nos" - no conversion needed
    return value;
  }

  throw new Error(`Cannot convert between different unit types: ${fromUnit} -> ${toUnit}`);
}

async function prepareClosingItems(tenantId, workAreaId, items) {
  // Combine all open Items into pkg and add last available price
  for (const element of items) {
    element['pkgId'] = element.pkg.id;
    
    // Convert ALL items (including "nos")
    const { equivalentPackages, closingQtyInRecipeUOM } = convertOpenToClosingQuantity(element);
    
    // Combine: total packages = closing + converted open
    const totalClosingQuantity = element.closingQuantity + equivalentPackages;
    
    // Update the element with BOTH values
    element.convertedClosingQuantity = totalClosingQuantity;
    element.closingQtyInRecipeUOM = closingQtyInRecipeUOM;
    
    // Fetch last GRN price
    const lastGrnPrice = await findLastGrnItemPrice({
      tenantId,
      itemId: element.itemId,
      inventoryLocationId: workAreaId,
      pkgId: element.pkg?.id,
    });
  
    // Update unit cost
    if (lastGrnPrice) {
      element.unitCost = lastGrnPrice.unitCost;
    }
  }
  
  return items;
}

// Helper function to save individual item to closing subcollection
async function saveClosingItem(closingId, item) {
  try {
    const closingRef = db.collection(CLOSING_COLLECTION).doc(closingId);
    const itemDocId = `${item.itemId}_${item.pkg.id}`;

    await closingRef
      .collection("items")
      .doc(itemDocId)
      .set(item);

    return { success: true, itemDocId };
  } catch (error) {
    console.error("Error saving closing item:", error);
    throw new Error(error.message);
  }
}

// Helper function to get all items from a closing subcollection
async function getClosingItems(closingId) {
  try {
    const closingRef = db.collection(CLOSING_COLLECTION).doc(closingId);
    const itemsSnapshot = await closingRef.collection("items").get();

    return itemsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error("Error getting closing items:", error);
    throw new Error(error.message);
  }
}

module.exports = {
  fetchClosingItems,
  createClosing,
  fetchClosingData,
  getById,
  getClosingByNumber,
  prepareClosingItems,
  saveClosingItem,
  getClosingItems,
};
