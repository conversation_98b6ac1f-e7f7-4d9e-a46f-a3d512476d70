/**
 * Example demonstrating how to use the new closing subcollection structure
 * 
 * Structure:
 * closings
 *  └── {closingId}              ← HEADER document
 *      ├── closingDate
 *      ├── locationId
 *      ├── workAreaId
 *      ├── stockCorrection
 *      └── closedBy
 * 
 *      └── items                ← SUB-COLLECTION (for items only)
 *          ├── {itemDocId_1}
 *          ├── {itemDocId_2}
 *          └── {itemDocId_N}
 */

const admin = require("firebase-admin");
const db = admin.firestore();
const { saveClosingItem, getClosingItems } = require("@/repositories/closingRepo");

// Example: Save an item to closing subcollection
async function saveItemToClosingExample() {
  const closingId = "your-closing-id-here";
  
  const item = {
    itemId: "item123",
    itemName: "Sample Item",
    itemCode: "SI001",
    categoryId: "cat1",
    subcategoryId: "subcat1",
    categoryName: "Category 1",
    subcategoryName: "Subcategory 1",
    unitCost: 10.50,
    purchaseUOM: "kg",
    countingUOM: "kg",
    recipeUOM: "kg",
    itemType: "bought",
    conversionFactor: 1,
    openQuantities: [],
    closingQuantity: 5,
    pkg: {
      id: "pkg123",
      name: "Package 1",
      packageCode: "PKG001",
      quantity: 1,
      unitCost: 10.50,
      emptyWeight: 0.1,
      fullWeight: 1.1,
      toUnit: "kg"
    }
  };

  try {
    // This is exactly what you wanted to achieve:
    await db
      .collection("closings")
      .doc(closingId)
      .collection("items")
      .doc(`${item.itemId}_${item.pkg.id}`)
      .set(item);
      
    console.log("Item saved successfully!");
    
    // Or use the helper function:
    // const result = await saveClosingItem(closingId, item);
    // console.log("Item saved using helper:", result);
    
  } catch (error) {
    console.error("Error saving item:", error);
  }
}

// Example: Get all items from a closing
async function getItemsFromClosingExample() {
  const closingId = "your-closing-id-here";
  
  try {
    // Direct Firestore query
    const itemsSnapshot = await db
      .collection("closings")
      .doc(closingId)
      .collection("items")
      .get();
      
    const items = itemsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
    
    console.log("Items retrieved:", items);
    
    // Or use the helper function:
    // const items = await getClosingItems(closingId);
    // console.log("Items retrieved using helper:", items);
    
  } catch (error) {
    console.error("Error getting items:", error);
  }
}

module.exports = {
  saveItemToClosingExample,
  getItemsFromClosingExample
};
